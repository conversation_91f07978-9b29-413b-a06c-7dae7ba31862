#!/bin/bash

echo "=== OpenAI API密钥验证脚本 - 最终修复验证 ==="
echo ""

# 检查脚本语法
echo "1. 检查脚本语法..."
if bash -n check.sh 2>/dev/null; then
    echo "✅ 语法检查通过"
else
    echo "❌ 语法检查失败"
    bash -n check.sh
    exit 1
fi

echo ""
echo "2. 验证修复内容..."

# 检查多个失败关键词支持
if grep -q "check_fail_keywords" check.sh; then
    echo "✅ 多个失败关键词支持已添加"
else
    echo "❌ 多个失败关键词支持缺失"
fi

# 检查关键词分割逻辑
if grep -q "IFS='#'" check.sh; then
    echo "✅ 关键词分割逻辑已实现"
else
    echo "❌ 关键词分割逻辑缺失"
fi

# 检查输出格式修复
if grep -q "不带序号，直接显示密钥" check.sh; then
    echo "✅ 输出格式已修复（去掉序号）"
else
    echo "❌ 输出格式未修复"
fi

# 检查最终输出说明
if grep -q "作为脚本返回值" check.sh; then
    echo "✅ 最终输出说明已优化"
else
    echo "❌ 最终输出说明未优化"
fi

# 检查脚本退出逻辑
if grep -q "确保脚本正确退出" check.sh; then
    echo "✅ 脚本退出逻辑已优化"
else
    echo "❌ 脚本退出逻辑未优化"
fi

echo ""
echo "3. 测试多个失败关键词功能..."

# 创建测试函数
cat > temp_test_keywords.sh << 'EOF'
#!/bin/bash

# 模拟check_fail_keywords函数
check_fail_keywords() {
    local response="$1"
    local keywords="$2"
    
    if [ -z "$keywords" ]; then
        return 1
    fi
    
    IFS='#' read -ra keyword_array <<< "$keywords"
    
    for keyword in "${keyword_array[@]}"; do
        keyword=$(echo "$keyword" | xargs)
        
        if [ -n "$keyword" ] && echo "$response" | grep -qi "$keyword"; then
            echo "$keyword"
            return 0
        fi
    done
    
    return 1
}

# 测试用例
echo "测试1: 单个关键词"
if matched=$(check_fail_keywords "Account suspended" "suspended"); then
    echo "✅ 匹配到: $matched"
else
    echo "❌ 未匹配"
fi

echo "测试2: 多个关键词（匹配第一个）"
if matched=$(check_fail_keywords "Account suspended" "suspended#invalid#error"); then
    echo "✅ 匹配到: $matched"
else
    echo "❌ 未匹配"
fi

echo "测试3: 多个关键词（匹配第二个）"
if matched=$(check_fail_keywords "Invalid API key" "suspended#invalid#error"); then
    echo "✅ 匹配到: $matched"
else
    echo "❌ 未匹配"
fi

echo "测试4: 带空格的关键词"
if matched=$(check_fail_keywords "Error occurred" " error # warning "); then
    echo "✅ 匹配到: $matched"
else
    echo "❌ 未匹配"
fi

echo "测试5: 无匹配"
if matched=$(check_fail_keywords "Success response" "suspended#invalid#error"); then
    echo "❌ 意外匹配到: $matched"
else
    echo "✅ 正确：无匹配"
fi
EOF

bash temp_test_keywords.sh
rm -f temp_test_keywords.sh

echo ""
echo "4. 验证输出格式..."
echo "现在的输出格式应该是："
echo "- 验证通过的有效密钥列表: 直接显示密钥，无序号"
echo "- 最终输出: 每行一个密钥，作为脚本返回值"
echo "- 标准输出: 只包含有效密钥，每行一个"
echo "- 其他信息: 通过stderr输出，不影响返回值"

echo ""
echo "=== 修复验证完成 ==="
echo ""
echo "主要修复内容:"
echo "✅ 1. 支持多个失败关键词（用#分隔）"
echo "✅ 2. 去掉有效密钥列表的序号"
echo "✅ 3. 修复最终输出显示问题"
echo "✅ 4. 优化脚本返回值逻辑"
echo "✅ 5. 完善错误处理和退出机制"
echo ""
echo "现在可以运行: bash check.sh test_keys.txt"
echo "支持的失败关键词格式: --fail-keyword \"suspended#invalid#error\""
