package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
	nativedialog "github.com/sqweek/dialog"
)

// 预置的API验证地址
var presetAPIs = []struct {
	Name string
	URL  string
}{
	{"OpenAI Models - 官方", "https://api.openai.com/v1/models"},
	{"OpenAI Models - 代理 - bbb", "https://api-proxy.me/openai/v1/models"},
	{"OpenAI Models - 代理 - chatwise", "https://ai-proxy.chatwise.app/openai/v1/models"},
	{"Google Gemini Models - 官方", "https://generativelanguage.googleapis.com/v1beta/openai/models"},
	{"Google Gemini Models - 代理 - bbb", "https://api-proxy.me/gemini/v1beta/openai/models"},
	{"Google Gemini Models - 代理 - chatwise", "https://ai-proxy.chatwise.app/generativelanguage/v1beta/openai/models"},
	{"自定义输入", ""},
}

// KeyValidator 主应用结构体
type KeyValidator struct {
	window         fyne.Window
	urlEntry       *widget.Entry       // API URL输入框
	presetButton   *widget.Button      // 预设API选择按钮
	mainInput      *widget.Entry       // 主输入文本域
	prefixEntry    *widget.Entry       // 密钥前缀输入框
	lengthEntry    *widget.Entry       // 密钥长度输入框
	resultOutput   *widget.Entry       // 结果输出文本域
	validateButton *widget.Button      // 校验按钮
	copyButton     *widget.Button      // 复制按钮
	fileButton     *widget.Button      // 文件选择按钮
	statusLabel    *widget.Label       // 状态标签
	progressBar    *widget.ProgressBar // 进度条
	asyncCheck     *widget.Check       // 异步请求复选框
	threadEntry    *widget.Entry       // 线程数输入框
	intervalEntry  *widget.Entry       // 请求间隔输入框
	keywordsEntry  *widget.Entry       // 失败关键词输入框
	mu             sync.Mutex          // 互斥锁保护并发访问
	cancelFunc     context.CancelFunc  // 取消函数
	validating     atomic.Bool         // 是否正在验证
	validKeyChan   chan string         // 用于传递有效密钥的通道
	validKeyCount  atomic.Int32        // 有效密钥计数
}

// KeyValidator 创建新的验证器实例
func NewKeyValidator() *KeyValidator {
	myApp := app.New()
	myApp.Settings().SetTheme(theme.LightTheme())

	window := myApp.NewWindow("API Key批量验证工具")
	window.Resize(fyne.NewSize(900, 850))
	window.CenterOnScreen()

	validator := &KeyValidator{
		window: window,
	}

	validator.createUI()
	return validator
}

// createUI 创建用户界面
func (kv *KeyValidator) createUI() {
	// 创建URL输入框
	kv.urlEntry = widget.NewEntry()
	kv.urlEntry.SetText("https://ai-proxy.chatwise.app/generativelanguage/v1beta/openai/models")
	kv.urlEntry.SetPlaceHolder("输入API验证地址... openai地址为 https://api.openai.com/v1/models , gemini地址为 https://generativelanguage.googleapis.com/v1beta/openai/models")

	// 创建预设选择按钮
	kv.presetButton = widget.NewButtonWithIcon("预设", theme.ListIcon(), kv.showPresetAPIs)

	// URL输入容器 - 输入框和预设按钮组合
	urlInputContainer := container.NewBorder(
		nil, nil, nil,
		kv.presetButton,
		kv.urlEntry,
	)

	urlContainer := container.NewBorder(
		nil, nil,
		widget.NewLabelWithStyle("验证接口:", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
		nil,
		urlInputContainer,
	)

	// 创建格式校验设置区域
	kv.prefixEntry = widget.NewEntry()
	kv.prefixEntry.SetText("")
	kv.prefixEntry.SetPlaceHolder("密钥前缀。  Gemini Key前缀为 AIzaSy")

	kv.lengthEntry = widget.NewEntry()
	kv.lengthEntry.SetText("0")
	kv.lengthEntry.SetPlaceHolder("0=不限制 。  Gemini Key长度为 39")

	prefixContainer := container.NewBorder(
		nil, nil,
		widget.NewLabel("前缀要求:"),
		nil,
		container.NewMax(kv.prefixEntry),
	)

	lengthContainer := container.NewBorder(
		nil, nil,
		widget.NewLabel("长度要求:"),
		nil,
		container.NewMax(kv.lengthEntry),
	)

	formatContainer := container.NewGridWithColumns(2,
		prefixContainer,
		lengthContainer,
	)

	formatHint := widget.NewLabelWithStyle(
		"提示：前缀不区分大小写，前缀留空表示不限制密钥前缀以什么开头. 长度设为0表示不限制长度",
		fyne.TextAlignCenter,
		fyne.TextStyle{Italic: true},
	)

	// 创建异步请求设置区域
	kv.asyncCheck = widget.NewCheck("启用异步请求", func(checked bool) {
		if checked {
			kv.threadEntry.Enable()
		} else {
			kv.threadEntry.Disable()
		}
	})

	kv.threadEntry = widget.NewEntry()
	kv.threadEntry.SetText("10")
	kv.threadEntry.SetPlaceHolder("线程数")
	kv.threadEntry.Disable()

	kv.intervalEntry = widget.NewEntry()
	kv.intervalEntry.SetText("0.5")
	kv.intervalEntry.SetPlaceHolder("秒")

	kv.keywordsEntry = widget.NewEntry()
	kv.keywordsEntry.SetText("suspended,invalid_api_key")
	kv.keywordsEntry.SetPlaceHolder("多个关键词用逗号分隔. Gemini Key封禁对应 suspended , OpenAI Key封禁对应 invalid_api_key")

	threadContainer := container.NewBorder(
		nil, nil,
		widget.NewLabel("线程数:"),
		nil,
		container.NewMax(kv.threadEntry),
	)

	intervalContainer := container.NewBorder(
		nil, nil,
		widget.NewLabel("请求间隔:"),
		widget.NewLabel("秒"),
		container.NewMax(kv.intervalEntry),
	)

	asyncSettingsContainer := container.NewGridWithColumns(2,
		threadContainer,
		intervalContainer,
	)

	asyncContainer := container.NewVBox(
		kv.asyncCheck,
		asyncSettingsContainer,
	)

	keywordsContainer := container.NewBorder(
		nil, nil,
		widget.NewLabel("失败关键词:"),
		nil,
		kv.keywordsEntry,
	)

	validationHint := widget.NewLabelWithStyle(
		"提示：请求间隔用于控制API调用频率；失败关键词用于判断密钥是否无效",
		fyne.TextAlignCenter,
		fyne.TextStyle{Italic: true},
	)

	// 创建主输入文本域
	kv.mainInput = widget.NewMultiLineEntry()
	kv.mainInput.SetPlaceHolder("请输入密钥、本地文件路径或网络地址（每行一个）...\n\n支持格式：\n- 直接输入密钥\n- 本地文件路径：C:\\keys.txt\n- 网络地址：https://example.com/keys.txt")
	kv.mainInput.Resize(fyne.NewSize(0, 180))

	// 创建文件选择按钮
	kv.fileButton = widget.NewButtonWithIcon("选择文件", theme.FolderOpenIcon(), kv.selectFile)

	// 创建校验按钮
	kv.validateButton = widget.NewButtonWithIcon("开始验证", theme.ConfirmIcon(), kv.onValidate)
	kv.validateButton.Importance = widget.HighImportance

	// 创建停止按钮
	stopButton := widget.NewButtonWithIcon("停止验证", theme.MediaStopIcon(), kv.stopValidation)
	stopButton.Importance = widget.DangerImportance
	stopButton.Disable()

	// 创建清空按钮
	clearButton := widget.NewButtonWithIcon("清空输入", theme.DeleteIcon(), func() {
		kv.mainInput.SetText("")
		kv.resultOutput.SetText("")
		kv.updateStatus("已清空输入内容")
	})

	// 创建按钮容器
	buttonContainer := container.NewHBox(
		kv.fileButton,
		clearButton,
		layout.NewSpacer(),
		stopButton,
		kv.validateButton,
	)

	// 创建结果输出文本域
	kv.resultOutput = widget.NewMultiLineEntry()
	kv.resultOutput.SetPlaceHolder("验证通过的密钥将显示在这里...")
	kv.resultOutput.Resize(fyne.NewSize(0, 250))
	kv.resultOutput.Disable()

	// 创建复制按钮
	kv.copyButton = widget.NewButtonWithIcon("复制有效密钥", theme.ContentCopyIcon(), kv.onCopy)
	kv.copyButton.Disable()

	// 创建导出按钮
	exportButton := widget.NewButtonWithIcon("导出到文件", theme.DocumentSaveIcon(), kv.exportResults)
	exportButton.Disable()

	// 创建状态标签
	kv.statusLabel = widget.NewLabel("就绪")

	// 创建进度条
	kv.progressBar = widget.NewProgressBar()
	kv.progressBar.Hide()

	// 创建结果按钮容器
	resultButtons := container.NewHBox(
		kv.copyButton,
		exportButton,
	)

	// 创建状态容器
	statusContainer := container.NewBorder(
		nil, nil,
		kv.statusLabel,
		resultButtons,
		kv.progressBar,
	)

	// 创建结果容器
	resultLabel := widget.NewLabelWithStyle("验证结果:", fyne.TextAlignLeading, fyne.TextStyle{Bold: true})

	resultContainer := container.NewBorder(
		container.NewVBox(
			resultLabel,
			widget.NewSeparator(),
		),
		statusContainer,
		nil, nil,
		container.NewScroll(kv.resultOutput),
	)

	// 组装主界面
	content := container.NewBorder(
		container.NewVBox(
			urlContainer,
			widget.NewSeparator(),
			formatContainer,
			formatHint,
			widget.NewSeparator(),
			asyncContainer,
			keywordsContainer,
			validationHint,
			widget.NewSeparator(),
		),
		resultContainer,
		nil, nil,
		container.NewBorder(
			widget.NewLabelWithStyle("输入区域:", fyne.TextAlignLeading, fyne.TextStyle{Bold: true}),
			buttonContainer,
			nil, nil,
			container.NewScroll(kv.mainInput),
		),
	)

	// 设置内容并添加内边距
	kv.window.SetContent(container.NewPadded(content))

	// 同步导出按钮状态
	kv.resultOutput.OnChanged = func(content string) {
		hasContent := strings.TrimSpace(content) != ""
		if hasContent {
			exportButton.Enable()
			kv.copyButton.Enable()
		} else {
			exportButton.Disable()
			kv.copyButton.Disable()
		}
	}

	// 同步停止按钮状态
	go func() {
		for {
			time.Sleep(100 * time.Millisecond)
			if kv.validating.Load() {
				stopButton.Enable()
			} else {
				stopButton.Disable()
			}
		}
	}()
}

// showPresetAPIs 显示预设API选择对话框
func (kv *KeyValidator) showPresetAPIs() {
	items := make([]string, len(presetAPIs))
	for i, api := range presetAPIs {
		if api.URL == "" {
			items[i] = api.Name
		} else {
			items[i] = fmt.Sprintf("%s - %s", api.Name, api.URL)
		}
	}

	// 创建列表
	list := widget.NewList(
		func() int { return len(items) },
		func() fyne.CanvasObject {
			return widget.NewLabel("template")
		},
		func(i widget.ListItemID, o fyne.CanvasObject) {
			o.(*widget.Label).SetText(items[i])
		},
	)

	// 创建自定义对话框
	customDialog := dialog.NewCustom("选择预设API", "取消", list, kv.window)
	customDialog.Resize(fyne.NewSize(600, 400))

	// 处理列表选择
	list.OnSelected = func(id widget.ListItemID) {
		if id < len(presetAPIs) {
			selected := presetAPIs[id]
			if selected.URL != "" {
				kv.urlEntry.SetText(selected.URL)
			}
			customDialog.Hide()
		}
	}

	customDialog.Show()
}

// selectFile 选择文件
func (kv *KeyValidator) selectFile() {
	filePath, err := nativedialog.File().
		Filter("文本文件", "txt", "md", "json", "html", "go", "py", "js").
		Filter("所有文件", "*").
		Title("选择要校验密钥的文件").
		Load()

	if err != nil {
		if err != nativedialog.ErrCancelled {
			dialog.ShowError(fmt.Errorf("选择文件失败: %v", err), kv.window)
		}
		return
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		dialog.ShowError(fmt.Errorf("读取文件失败: %v", err), kv.window)
		return
	}

	kv.mu.Lock()
	currentText := kv.mainInput.Text
	if currentText != "" && !strings.HasSuffix(currentText, "\n") {
		currentText += "\n"
	}
	kv.mainInput.SetText(currentText + string(content))
	kv.mu.Unlock()

	fileName := filePath
	if idx := strings.LastIndex(filePath, string(os.PathSeparator)); idx >= 0 {
		fileName = filePath[idx+1:]
	}
	kv.updateStatus(fmt.Sprintf("已加载文件: %s", fileName))
}

// stopValidation 停止验证
func (kv *KeyValidator) stopValidation() {
	if kv.cancelFunc != nil {
		kv.cancelFunc()
		kv.updateStatus("正在停止验证...")
	}
}

// onValidate 执行验证
func (kv *KeyValidator) onValidate() {
	if strings.TrimSpace(kv.mainInput.Text) == "" {
		dialog.ShowInformation("提示", "请先输入要验证的密钥或文件路径", kv.window)
		return
	}

	if kv.validating.Load() {
		dialog.ShowInformation("提示", "正在验证中，请稍候...", kv.window)
		return
	}

	kv.validating.Store(true)
	kv.validateButton.Disable()
	kv.resultOutput.SetText("")
	kv.progressBar.Show()
	kv.progressBar.SetValue(0)

	// 初始化通道和计数器
	kv.validKeyChan = make(chan string, 1000)
	kv.validKeyCount.Store(0)

	// 创建可取消的context
	ctx, cancel := context.WithCancel(context.Background())
	kv.cancelFunc = cancel

	go func() {
		defer func() {
			kv.validating.Store(false)
			kv.validateButton.Enable()
			kv.progressBar.Hide()
			kv.cancelFunc = nil
		}()

		keys := kv.collectKeys()
		if len(keys) == 0 {
			kv.updateStatus("未找到有效的密钥")
			return
		}

		keys = kv.removeDuplicates(keys)
		kv.updateStatus(fmt.Sprintf("已收集 %d 个待验证密钥（已去重）...", len(keys)))

		formattedKeys := kv.validateFormat(keys)
		if len(formattedKeys) == 0 {
			kv.updateStatus("没有密钥通过格式验证")
			dialog.ShowError(fmt.Errorf("所有密钥都不符合格式要求"), kv.window)
			return
		}

		kv.updateStatus(fmt.Sprintf("格式验证通过: %d 个密钥。开始API验证...", len(formattedKeys)))

		// validateAPI现在是流式处理，直接更新UI，并返回有效密钥总数
		validCount := kv.validateAPI(ctx, formattedKeys)

		// 检查是否被取消
		select {
		case <-ctx.Done():
			kv.updateStatus("验证已停止")
			return
		default:
		}

		if validCount > 0 {
			kv.updateStatus(fmt.Sprintf("验证完成！找到 %d 个有效密钥", validCount))
			dialog.ShowInformation("验证完成",
				fmt.Sprintf("验证完成！\n\n总计: %d 个密钥\n格式正确: %d 个\n验证通过: %d 个",
					len(keys), len(formattedKeys), validCount),
				kv.window)
		} else {
			kv.updateStatus("未找到有效密钥")
			dialog.ShowError(fmt.Errorf("所有密钥验证都失败了"), kv.window)
		}
	}()
}

// exportResults 导出结果到文件
func (kv *KeyValidator) exportResults() {
	content := kv.resultOutput.Text
	if content == "" {
		return
	}

	filePath, err := nativedialog.File().
		Filter("文本文件", "txt").
		Filter("所有文件", "*").
		Title("保存有效密钥").
		Save()
	if err != nil {
		if err != nativedialog.ErrCancelled {
			dialog.ShowError(fmt.Errorf("保存文件失败: %v", err), kv.window)
		}
		return
	}

	if !strings.HasSuffix(filePath, ".txt") {
		filePath += ".txt"
	}

	err = os.WriteFile(filePath, []byte(content), 0644)
	if err != nil {
		dialog.ShowError(fmt.Errorf("写入文件失败: %v", err), kv.window)
		return
	}

	fileName := filePath
	if idx := strings.LastIndex(filePath, string(os.PathSeparator)); idx >= 0 {
		fileName = filePath[idx+1:]
	}
	kv.updateStatus(fmt.Sprintf("已导出到文件: %s", fileName))
	dialog.ShowInformation("导出成功", fmt.Sprintf("有效密钥已保存到:\n%s", filePath), kv.window)
}

// removeDuplicates 去除重复的密钥
func (kv *KeyValidator) removeDuplicates(keys []string) []string {
	seen := make(map[string]bool)
	result := []string{}
	for _, key := range keys {
		if !seen[key] {
			seen[key] = true
			result = append(result, key)
		}
	}
	return result
}

// collectKeys 收集所有待验证的密钥
func (kv *KeyValidator) collectKeys() []string {
	var allKeys []string
	lines := strings.Split(kv.mainInput.Text, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		if kv.isFilePath(line) {
			keys := kv.readLocalFile(line)
			allKeys = append(allKeys, keys...)
			kv.updateStatus(fmt.Sprintf("已读取文件: %s (%d个密钥)", line, len(keys)))
		} else if kv.isURL(line) {
			keys := kv.readRemoteFile(line)
			allKeys = append(allKeys, keys...)
			kv.updateStatus(fmt.Sprintf("已读取URL: %s (%d个密钥)", line, len(keys)))
		} else {
			allKeys = append(allKeys, line)
		}
	}

	return allKeys
}

// isFilePath 检查是否为本地文件路径
func (kv *KeyValidator) isFilePath(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return !info.IsDir()
}

// isURL 检查是否为URL
func (kv *KeyValidator) isURL(str string) bool {
	u, err := url.Parse(str)
	if err != nil {
		return false
	}
	return (u.Scheme == "http" || u.Scheme == "https") && u.Host != ""
}

// readLocalFile 读取本地文件
func (kv *KeyValidator) readLocalFile(path string) []string {
	var keys []string
	file, err := os.Open(path)
	if err != nil {
		kv.updateStatus(fmt.Sprintf("读取文件失败 %s: %v", path, err))
		return keys
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	buf := make([]byte, 0, 64*1024)
	scanner.Buffer(buf, 1024*1024)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			keys = append(keys, line)
		}
	}

	if err := scanner.Err(); err != nil {
		kv.updateStatus(fmt.Sprintf("扫描文件错误 %s: %v", path, err))
	}

	return keys
}

// readRemoteFile 读取远程文件
func (kv *KeyValidator) readRemoteFile(urlStr string) []string {
	var keys []string
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Get(urlStr)
	if err != nil {
		kv.updateStatus(fmt.Sprintf("获取URL失败 %s: %v", urlStr, err))
		return keys
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		kv.updateStatus(fmt.Sprintf("URL返回错误状态 %s: %d", urlStr, resp.StatusCode))
		return keys
	}

	scanner := bufio.NewScanner(resp.Body)
	buf := make([]byte, 0, 64*1024)
	scanner.Buffer(buf, 1024*1024)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			keys = append(keys, line)
		}
	}

	if err := scanner.Err(); err != nil {
		kv.updateStatus(fmt.Sprintf("读取URL内容错误 %s: %v", urlStr, err))
	}

	return keys
}

// validateFormat 验证密钥格式
func (kv *KeyValidator) validateFormat(keys []string) []string {
	var validKeys []string
	prefix := strings.ToLower(strings.TrimSpace(kv.prefixEntry.Text))
	lengthStr := strings.TrimSpace(kv.lengthEntry.Text)
	length, err := strconv.Atoi(lengthStr)
	if err != nil {
		length = 0
	}

	for _, key := range keys {
		key = strings.TrimSpace(key)
		if key == "" {
			continue
		}

		if prefix != "" && !strings.HasPrefix(strings.ToLower(key), prefix) {
			continue
		}

		if length > 0 && len(key) != length {
			continue
		}

		validKeys = append(validKeys, key)
	}

	return validKeys
}

// validateAPI 通过API验证密钥
func (kv *KeyValidator) validateAPI(ctx context.Context, keys []string) int32 {
	apiURL := strings.TrimSpace(kv.urlEntry.Text)
	if apiURL == "" {
		kv.updateStatus("API URL不能为空")
		return 0
	}

	// 启动UI更新的消费者goroutine
	var wgUI sync.WaitGroup
	wgUI.Add(1)
	go func() {
		defer wgUI.Done()
		kv.batchUpdateUI(ctx)
	}()

	keywordsStr := strings.TrimSpace(kv.keywordsEntry.Text)
	failKeywords := []string{}
	if keywordsStr != "" {
		for _, keyword := range strings.Split(keywordsStr, ",") {
			keyword = strings.TrimSpace(keyword)
			if keyword != "" {
				failKeywords = append(failKeywords, strings.ToLower(keyword))
			}
		}
	}

	intervalStr := strings.TrimSpace(kv.intervalEntry.Text)
	interval, err := strconv.ParseFloat(intervalStr, 64)
	if err != nil || interval < 0 {
		interval = 0.5
	}
	intervalDuration := time.Duration(interval * float64(time.Second))

	if kv.asyncCheck.Checked {
		// 异步验证
		threadCount, err := strconv.Atoi(kv.threadEntry.Text)
		if err != nil || threadCount <= 0 {
			threadCount = 5
		}

		keyChan := make(chan struct {
			key   string
			index int
		}, len(keys))
		var completed int32
		totalKeys := int32(len(keys))

		var wg sync.WaitGroup
		for i := 0; i < threadCount; i++ {
			wg.Add(1)
			go func(workerID int) {
				defer wg.Done()
				client := &http.Client{Timeout: 15 * time.Second}

				for item := range keyChan {
					select {
					case <-ctx.Done():
						return
					default:
					}

					valid := kv.validateSingleKey(ctx, client, apiURL, item.key, failKeywords)
					if valid {
						kv.validKeyCount.Add(1)
						kv.validKeyChan <- item.key
					}

					current := atomic.AddInt32(&completed, 1)
					progress := float64(current) / float64(totalKeys)
					// 安全地更新UI
					kv.window.Canvas().Refresh(kv.progressBar)
					kv.progressBar.SetValue(progress)
					kv.updateStatus(fmt.Sprintf("正在验证密钥 %d/%d (%.0f%%) - 异步模式", current, totalKeys, progress*100))

					if item.index < len(keys) && intervalDuration > 0 {
						time.Sleep(intervalDuration)
					}
				}
			}(i)
		}

	Loop:
		for i, key := range keys {
			select {
			case <-ctx.Done():
				break Loop
			case keyChan <- struct {
				key   string
				index int
			}{key, i + 1}:
			}
		}
		close(keyChan)
		wg.Wait()
	} else {
		// 同步验证
		client := &http.Client{Timeout: 15 * time.Second}
		totalKeys := len(keys)

	LoopSync:
		for i, key := range keys {
			select {
			case <-ctx.Done():
				break LoopSync
			default:
			}

			progress := float64(i+1) / float64(totalKeys)
			kv.progressBar.SetValue(progress)
			kv.updateStatus(fmt.Sprintf("正在验证密钥 %d/%d (%.0f%%)", i+1, totalKeys, progress*100))

			if kv.validateSingleKey(ctx, client, apiURL, key, failKeywords) {
				kv.validKeyCount.Add(1)
				kv.validKeyChan <- key
			}

			if i < totalKeys-1 && intervalDuration > 0 {
				time.Sleep(intervalDuration)
			}
		}
	}

	// 所有验证任务完成，关闭通道以通知UI更新goroutine
	close(kv.validKeyChan)
	// 等待UI更新完成
	wgUI.Wait()

	return kv.validKeyCount.Load()
}

// batchUpdateUI 是一个运行在单独goroutine中的函数，用于批量更新UI
func (kv *KeyValidator) batchUpdateUI(ctx context.Context) {
	const batchSize = 50                        // 每50个key更新一次UI
	const batchTimeout = 500 * time.Millisecond // 或者每500毫秒更新一次

	var keyBatch []string
	ticker := time.NewTicker(batchTimeout)
	defer ticker.Stop()

	for {
		select {
		case key, ok := <-kv.validKeyChan:
			if !ok {
				// 通道已关闭, 处理最后一批数据
				if len(keyBatch) > 0 {
					kv.appendResultsToUI(keyBatch)
				}
				return
			}
			keyBatch = append(keyBatch, key)
			if len(keyBatch) >= batchSize {
				kv.appendResultsToUI(keyBatch)
				keyBatch = nil // 清空批次
			}
		case <-ticker.C:
			// 超时, 处理当前批次
			if len(keyBatch) > 0 {
				kv.appendResultsToUI(keyBatch)
				keyBatch = nil // 清空批次
			}
		case <-ctx.Done():
			// 如果上下文被取消，也处理最后一批
			if len(keyBatch) > 0 {
				kv.appendResultsToUI(keyBatch)
			}
			return
		}
	}
}

// appendResultsToUI 线程安全地将一批密钥追加到结果区域
func (kv *KeyValidator) appendResultsToUI(keys []string) {
	if len(keys) == 0 {
		return
	}

	kv.mu.Lock()
	defer kv.mu.Unlock()

	newText := strings.Join(keys, "\n")
	currentText := kv.resultOutput.Text

	var builder strings.Builder
	builder.Grow(len(currentText) + len(newText) + 1)
	builder.WriteString(currentText)
	if currentText != "" {
		builder.WriteString("\n")
	}
	builder.WriteString(newText)

	kv.resultOutput.SetText(builder.String())
}

// validateSingleKey 验证单个密钥
func (kv *KeyValidator) validateSingleKey(ctx context.Context, client *http.Client, apiURL, key string, failKeywords []string) bool {
	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return false
	}

	// 设置请求头
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", key))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36")
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")

	resp, err := client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false
	}

	if resp.StatusCode != 200 {
		return false
	}

	bodyStr := strings.ToLower(string(body))
	for _, keyword := range failKeywords {
		if strings.Contains(bodyStr, keyword) {
			return false
		}
	}

	return true
}

// onCopy 复制有效密钥到剪贴板
func (kv *KeyValidator) onCopy() {
	content := kv.resultOutput.Text
	if content == "" {
		return
	}
	clipboard := kv.window.Clipboard()
	clipboard.SetContent(content)
	originalText := kv.copyButton.Text
	kv.copyButton.SetText("已复制!")
	kv.copyButton.Disable()
	go func() {
		time.Sleep(2 * time.Second)
		kv.copyButton.SetText(originalText)
		kv.copyButton.Enable()
	}()
	kv.updateStatus("有效密钥已复制到剪贴板！")
}

// updateStatus 更新状态标签（线程安全）
func (kv *KeyValidator) updateStatus(message string) {
	kv.mu.Lock()
	defer kv.mu.Unlock()
	kv.statusLabel.SetText(message)
}

// Run 运行应用
func (kv *KeyValidator) Run() {
	kv.window.ShowAndRun()
}

func main() {
	validator := NewKeyValidator()
	validator.Run()
}
